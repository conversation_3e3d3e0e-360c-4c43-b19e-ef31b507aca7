# 🚀 Innovationx Range Master Pro v2.0

**Professional Range Breakout Trading System for MetaTrader 4**

---

## 🏢 Company & Developer Information

**Company:** Innovationx International
**Website:** [https://innovationxinternational.com](https://innovationxinternational.com)
**Developer:** <PERSON>
**Email:** <EMAIL>
**Copyright:** 2025, Innovationx International
**Version:** 2.0
**Platform:** MetaTrader 4 (MT4)

---

## 📋 Overview

Innovationx Range Master Pro is a sophisticated and highly customizable Expert Advisor (EA) designed for professional range breakout trading on MetaTrader 4. This advanced trading system combines traditional range breakout strategies with cutting-edge features including enhanced fakeout detection, multi-timeframe analysis, comprehensive risk management, and intelligent trade management.

### 🎯 Key Trading Strategy

The EA identifies price ranges during specified quiet periods (typically overnight or low-volatility sessions) and places pending orders to capture breakouts when the market becomes active. It employs advanced logic to distinguish between genuine breakouts and false breakouts (fakeouts), providing traders with a robust edge in volatile markets.

### 🌟 What Makes This EA Special

- **Multi-Asset Compatibility:** Optimized for Forex, Indices, Commodities, and Cryptocurrencies
- **Enhanced Fakeout Detection:** Advanced logic to trade against false breakouts
- **Comprehensive Risk Management:** Multiple layers of protection including daily/weekly loss limits
- **Intelligent Trade Management:** Breakeven, trailing stops, and multiple exit strategies
- **Visual Trading Interface:** Real-time statistics panel and range visualization
- **Flexible Session Management:** Customizable trading hours and range identification periods

---

## 🔧 Core Features & Configuration

The EA is organized into 11 comprehensive sections, each addressing specific aspects of trading strategy and risk management:

### 💰 SECTION 1: Trade Sizing & Risk Management

**Purpose:** Configure position sizing, risk per trade, and maximum concurrent positions.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `LotSize` | double | 0.01 | Default trading lot size for all trades |
| `InitialTradeRiskPercent` | double | 0 | % of balance to risk on first trade (0 = use min lot) |
| `UseFixedRisk` | bool | false | Enable fixed money risk per trade |
| `RiskAmount` | double | 17.0 | Fixed money amount to risk per trade |
| `RiskPercent` | double | 1.0 | Percentage risk per trade |
| `UsePercentRisk` | bool | false | Use percentage risk instead of fixed amount |
| `PointValue` | double | 0.20 | Value of 1 point in account currency |
| `MaxTrades` | int | 1 | Maximum trades per direction at any time |
| `MaxDailyTradesCount` | int | 0 | Maximum total number of trades the EA can open within a 24-hour period (0 = unlimited). Resets at midnight. |

**💡 Pro Tip:** For conservative trading, use 1-2% risk per trade. For aggressive strategies, consider 3-5% but never exceed 10%.

### 🎯 SECTION 2: Profit Targets

**Purpose:** Define take profit rules and exit strategies.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `TradeTargetR` | double | 1.5 | Take profit as multiple of stop loss (R-multiple) |
| `TradeTargetPoints` | double | 0 | Take profit in points (overrides R if >0) |

**💡 Strategy Note:** R-multiple of 1.5-2.0 provides good risk-reward ratio for range breakouts.

### 🛡️ SECTION 3: Stop Loss & Protection

**Purpose:** Configure stop loss methods and protective measures.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `StopLossMethod` | enum | SL_RANGE | Method for setting stop loss |
| `UseRangeStopLoss` | bool | true | (Deprecated) Use range boundaries as SL |
| `AddedStopLoss` | int | 0 | Additional points for SL (with SL_RANGE) |
| `FixedStopLossPoints` | int | 0 | Fixed SL distance in points (with SL_POINTS) |
| `FixedStopLossPrice` | double | 0.0 | Fixed SL price level (with SL_PRICE) |

**Stop Loss Methods:**
- **SL_RANGE:** Uses range boundaries with optional buffer points
- **SL_POINTS:** Fixed distance in points from entry price
- **SL_PRICE:** Fixed price level regardless of entry
- **SL_MIDRANGE:** Uses range midpoint with optional buffer points (NEW)

### ⚖️ SECTION 4: Breakeven Logic

**Purpose:** Automatically move stop loss to breakeven when trade becomes profitable.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `UseBreakEven` | bool | true | Enable breakeven functionality |
| `BreakEvenPoints` | int | 2500 | Points profit before moving to breakeven |
| `UseBreakEvenPercent` | bool | false | Use percentage instead of points |
| `BreakEvenPercent` | double | 0.0 | Percentage profit trigger (e.g., 1.0 = 1%) |
| `BETriggerPoints` | int | 0 | Alternative points trigger |
| `BETriggerPercent` | double | 0 | Alternative percentage trigger |

**💡 Asset-Specific Defaults:**
- **Forex:** 300 points (auto-set)
- **Indices/Metals/Crypto:** 3000 points (auto-set)

### 📈 SECTION 5: Trailing Stop Management

**Purpose:** Lock in profits as trade moves favorably.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `UseTrailingStop` | bool | true | Enable trailing stop functionality |
| `TrailingStopPoints` | int | 5000 | Distance for trailing stop in points |
| `UseTrailingStopPercent` | bool | false | Use percentage for trailing distance |
| `TrailingStopPercent` | double | 0.0 | Percentage distance (e.g., 1.0 = 1%) |

### ⏰ SECTION 6: Range & Session Timing

**Purpose:** Define when ranges are identified and trading sessions.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `RangeStartHour` | int | 0 | Hour when range identification starts (0-23) |
| `RangeStartMinute` | int | 0 | Minute when range identification starts |
| `RangeEndHour` | int | 6 | Hour when range identification ends |
| `RangeEndMinute` | int | 0 | Minute when range identification ends |
| `SessionPreset` | int | 0 | Preset trading session (0=24hr, 1=London, etc.) |
| `CustomSessionStartHour` | int | 0 | Custom session start hour |
| `CustomSessionEndHour` | int | 23 | Custom session end hour |

**🕐 Optimal Range Times by Asset:**
- **Indices:** 23:00-08:00 (captures overnight ranges)
- **Forex:** 02:00-07:00 (pre-London session)
- **Gold:** 21:00-07:00 (Asian + pre-London)

### 🎯 SECTION 7: Entry Buffer & Filtering

**Purpose:** Fine-tune entry conditions and filtering logic.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `EnableDynamicEntryBuffer` | bool | false | Use dynamic entry buffer |
| `EntryBufferPoints` | int | 20 | Entry buffer in points |
| `EntryBufferPercent` | double | 0.1 | Entry buffer as % of range |
| `AlwaysUseMinLot` | bool | false | Force minimum lot size |

### 🔢 SECTION 8: Multi-Trade & Per-Trade Settings

**Purpose:** Configure multiple trades per breakout with individual settings.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `NumBreakoutTrades` | int | 1 | Number of breakout trades per side |
| `LotSize1/2/3` | double | 0.01 | Individual lot sizes for trades 1-3 |
| `TP1/2/3` | double | 0 | Individual take profits (0 = use default) |
| `SL1/2/3` | int | 0 | Individual stop loss offsets (0 = use default) |
| `Trade1/2/3Rule` | int | 0 | Trade rules (0=Normal, 1=TightSL, 2=BreakevenRunner) |
| `Comment1/2/3` | string | "Trade X" | Individual trade comments |
| `Trade1/2/3Color` | color | Various | Individual trade colors |
| `EnableStaggeredOrders` | bool | false | Enable staggered order entries |
| `StaggeredOrderPercent` | double | 0.2 | Stagger offset percentage |
| `TightSL` | bool | false | Enable tight stop loss |
| `TightSLPoints` | int | 100 | Tight stop loss distance |
| `BreakevenRunner` | bool | false | Enable breakeven runner logic |
| `SymbolsList` | string | "EURUSD,GBPUSD,USDJPY" | Multi-symbol trading list |

**🎯 Advanced Features:**
- **Reversal Entry Logic:** Places additional trades when price re-enters range after breakout
- **Staggered Orders:** Multiple entry levels for better average price
- **Individual Trade Rules:** Different management for each trade position

### 🚨 SECTION 9: Risk & Range Filters

**Purpose:** Comprehensive risk management and trade filtering.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `MinRangePoints` | double | 1000 | Minimum range size to trade |
| `MaxRangePoints` | double | 0 | Maximum range size to trade (0 = disabled) (NEW) |
| `MaxSpreadPoints` | double | 50 | Maximum spread allowed |
| `PendingOrderExpiryHours` | int | 6 | Auto-cancel pending orders after X hours |
| `MaxDailyLoss` | double | 4.0 | Maximum daily loss percentage |
| `MaxWeeklyLoss` | double | 10.0 | Maximum weekly loss percentage |
| `EmergencyStopEquityPercent` | double | 80.0 | Emergency stop at equity % of starting balance |

**🛡️ Multi-Layer Protection:**
- **Daily Loss Limit:** Stops trading when daily loss threshold reached
- **Weekly Loss Limit:** Prevents excessive weekly drawdown
- **Emergency Stop:** Closes all trades if equity drops significantly
- **Automatic Order Cleanup:** Removes stale pending orders
- **Dynamic Range Filter:** Prevents trading when range is too small or too large (NEW)

### 📊 SECTION 10: Volatility & ADR/ATR Filters

**Purpose:** Filter trades based on market volatility and average daily range.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `EnableATRFilter` | bool | false | Use ATR filter for range validation |
| `ATRPeriod` | int | 14 | ATR calculation period |
| `MinRangeATR` | double | 0.5 | Minimum range as ATR multiple |
| `MaxRangeATR` | double | 2.5 | Maximum range as ATR multiple |
| `UseADRLimit` | bool | false | Use ADR-based range filter |
| `ADRDays` | int | 14 | ADR calculation period (days) |
| `MaxRangeADRMultiple` | double | 1.5 | Max range as ADR multiple |
| `ADRType` | int | 0 | ADR type (0=Daily, 1=Weekly) |

### 🎨 SECTION 11: Visuals, Alerts & Panels

**Purpose:** Customize chart appearance and monitoring features.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `RangeBoxColor` | color | clrYellow | Color for range box fill |
| `RangeTopLineColor` | color | clrRed | Color for range top boundary |
| `RangeBottomLineColor` | color | clrLime | Color for range bottom boundary |
| `RangeBoxOpacity` | int | 20 | Box fill opacity (0-255) |
| `EnableStatsPanel` | bool | true | Show real-time statistics panel |
| `EnableAlerts` | bool | true | Enable trading alerts |

### 🆕 NEW FEATURES (v2.1)

#### **Mid-Range Stop Loss (SL_MIDRANGE)**
A new stop loss method that places the stop loss at the midpoint between range high and range low, rather than at the range boundaries. This provides:
- **Tighter Risk Control:** Smaller stop losses for better risk-reward ratios
- **Reduced Drawdown:** Less exposure per trade
- **Faster Breakeven:** Quicker path to profitability

**Usage Example:**
```
StopLossMethod = SL_MIDRANGE
AddedStopLoss = 50  // Additional buffer points from midpoint
```

#### **Dynamic Range Filter (MaxRangePoints)**
A new filtering mechanism that prevents trading when ranges are too large, helping to avoid:
- **Excessive Risk:** Large ranges = large stop losses
- **Low Probability Setups:** Oversized ranges often indicate choppy markets
- **Poor Risk-Reward:** Large ranges reduce profit potential

**Usage Example:**
```
MinRangePoints = 500   // Don't trade if range < 500 points
MaxRangePoints = 3000  // Don't trade if range > 3000 points (0 = disabled)
```

### 🔍 SECTION 12: Fakeout & Reversal Logic

**Purpose:** Advanced fakeout detection and reversal entry strategies.

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `EnableFakeoutFade` | bool | false | Enable basic fakeout logic (fade trades against initial breakout) |
| `SessionOpenHour` | int | 9 | Session open hour for basic fakeout detection |
| `SessionOpenMinute` | int | 0 | Session open minute for basic fakeout detection |
| `EnableCustomTradingHours` | bool | false | Enable custom trading hours for main breakout logic |
| `CustomTradingStartHour` | int | 8 | Custom trading session start hour |
| `CustomTradingEndHour` | int | 17 | Custom trading session end hour |
| `EnableEnhancedFakeout` | bool | false | Enable enhanced fakeout logic (trades against range breakouts) |
| `EnableFakeoutCustomHours` | bool | false | Enable custom trading hours for enhanced fakeout logic |
| `FakeoutStartHour` | int | 0 | Fakeout session start hour |
| `FakeoutStartMinute` | int | 0 | Fakeout session start minute |
| `FakeoutEndHour` | int | 23 | Fakeout session end hour |
| `FakeoutEndMinute` | int | 59 | Fakeout session end minute |
| `EnableReversalEntryLogic` | bool | false | Toggle to enable or disable the 'Reversal Entry Logic' independently. When disabled, the EA will not attempt to place reversal trades. |
| `MaxDailyReversalTrades` | int | 0 | Maximum number of trades placed specifically by the 'Reversal Entry Logic' within a 24-hour period (0 = unlimited). Resets at midnight. |

**🧠 Fakeout & Reversal Logic Explained:**
- **Enhanced Fakeout Logic:** Detects false breakouts (price briefly breaks a range and then reverses) and places trades in the opposite direction. This logic is active during specified "Fakeout Trading Hours" or 24/7 if custom hours are disabled.
- **Reversal Entry Logic:** When enabled, this logic places additional trades when price re-enters the identified range after an initial breakout. It provides a secondary entry opportunity based on range re-tests.
- **Smart Timing:** Both fakeout and reversal logic can be configured with specific trading hours for better accuracy and control.

---

## 🚀 Installation & Setup Guide

### Step 1: Installation
1. **Download Files:** Ensure you have the main EA file: `Innovationx International Range Breakout X.mq4`
2. **Copy to MT4:** Place the `.mq4` file in your MetaTrader 4 `MQL4\Experts` folder
3. **Compile:** Open MetaEditor (F4 in MT4), open the file, and click "Compile" (F7)
4. **Restart MT4:** Close and reopen MetaTrader 4 to load the EA

### Step 2: Chart Setup
1. **Attach EA:** Drag the EA from Navigator → Expert Advisors onto your desired chart
2. **Enable AutoTrading:** Click the "AutoTrading" button in MT4 toolbar (should be green)
3. **Allow DLL Imports:** In EA settings, check "Allow DLL imports" if required
4. **Confirm Settings:** Ensure "Allow Algo Trading" is enabled in EA properties

### Step 3: Configuration
1. **Open Settings:** Right-click on EA → Expert Advisors → Properties → Inputs tab
2. **Configure Sections:** Adjust parameters according to your strategy (see sections above)
3. **Test Settings:** Start with conservative settings and adjust based on performance
4. **Save Template:** Save your configuration as a template for future use

---

## 📈 Trading Strategy & Best Practices

### 🎯 Recommended Asset Classes

| Asset Type | Range Hours | Breakout Session | Risk % | Notes |
|------------|-------------|------------------|---------|-------|
| **Forex Major Pairs** | 02:00-07:00 GMT | London Open | 1-2% | Best with EURUSD, GBPUSD |
| **Indices (US30, NAS100)** | 23:00-08:00 GMT | Market Open | 2-3% | High volatility, larger ranges |
| **Gold/Silver** | 21:00-07:00 GMT | Asian + London | 1.5-2.5% | Volatile, use tight risk management |
| **Crypto (BTC, ETH)** | 00:00-06:00 GMT | Various | 2-4% | 24/7 markets, adjust for volatility |

### 🛡️ Risk Management Guidelines

**Conservative Approach:**
- Risk per trade: 1-2%
- Max daily loss: 3-4%
- Max weekly loss: 8-10%
- Use SL_RANGE method

**Aggressive Approach:**
- Risk per trade: 3-5%
- Max daily loss: 5-6%
- Max weekly loss: 12-15%
- Consider SL_POINTS method

### 📊 Performance Monitoring

The EA includes a comprehensive statistics panel showing:
- **Win Rate:** Percentage of profitable trades
- **Expectancy:** Average profit per trade
- **Drawdown:** Maximum equity decline
- **Daily/Weekly P&L:** Current period performance
- **Range Statistics:** Current range size vs. ADR/ATR

---

## 🔧 Advanced Features

### Magic Number System
- **Base Magic:** 10000 + ChartID
- **Buy Orders:** MagicBase + 2000 + trade_number
- **Sell Orders:** MagicBase + 4000 + trade_number
- **Enhanced Fakeout Buy:** MagicBase + 5000
- **Enhanced Fakeout Sell:** MagicBase + 6000

### Automatic Daily Reset
- Range identification resets at midnight
- Loss limits reset daily/weekly
- Pending orders cleaned up automatically
- Performance statistics updated continuously

### Multi-Symbol Support
Configure `SymbolsList` parameter to trade multiple instruments simultaneously with the same settings.

---

## 🆘 Troubleshooting

### Common Issues

**EA Not Trading:**
- Check AutoTrading is enabled (green button)
- Verify range times are correctly set
- Ensure minimum range size is met
- Check spread limits

**Orders Not Placing:**
- Verify broker allows pending orders
- Check minimum distance requirements
- Ensure sufficient margin
- Review risk management limits

**Unexpected Behavior:**
- Check MT4 terminal logs
- Verify EA inputs are valid
- Ensure stable internet connection
- Contact support if issues persist

---

## 📞 Professional Support & Custom Development

### Contact Information
**Developer:** Bryan Alvin Bagorogoza
**Email:** <EMAIL>
**Company:** Innovationx International
**Website:** [https://innovationxinternational.com](https://innovationxinternational.com)

### Services Available
- **Custom EA Development:** Tailored trading systems for specific strategies
- **Indicator Creation:** Custom technical indicators and market analysis tools
- **Strategy Optimization:** Performance tuning and backtesting services
- **Integration Services:** API connections and multi-platform solutions
- **Training & Consultation:** One-on-one trading system education

### Why Choose Innovationx International?
- ✅ **Proven Track Record:** Years of experience in algorithmic trading
- ✅ **Professional Quality:** Enterprise-grade code and testing standards
- ✅ **Ongoing Support:** Continuous updates and technical assistance
- ✅ **Custom Solutions:** Tailored development for unique requirements
- ✅ **Competitive Pricing:** Professional services at reasonable rates

---

## 📄 Legal & Disclaimer

**Copyright Notice:** This software is protected by copyright law. Unauthorized distribution, modification, or reverse engineering is prohibited.

**Trading Risk Warning:** Trading foreign exchange, indices, commodities, and cryptocurrencies involves substantial risk and may not be suitable for all investors. Past performance is not indicative of future results. Only trade with money you can afford to lose.

**No Warranty:** This software is provided "as is" without warranty of any kind. The developer assumes no responsibility for trading losses or system failures.

---

*© 2025 Innovationx International - Professional Trading Solutions*

**Version:** 2.0 | **Last Updated:** January 2025 | **Platform:** MetaTrader 4

---

# Innovationx International Range Breakout X

**Professional Range Breakout Trading System for MT4**  
Designed by Bryan Alvin Bagorogoza  
Copyright 2025, Innovationx International  
https://innovationxinternational.com

## Overview

This Expert Advisor (EA) implements a robust range breakout strategy for MetaTrader 4. It is designed to identify price ranges during quiet market sessions and place breakout trades when price exceeds these ranges. The EA includes advanced risk management, trailing stop, breakeven, and fakeout/reversal logic.

## Key Features

- **Configurable Range Times:** Define custom range periods and trading sessions.
- **Multi-Trade Support:** Place multiple breakout trades per direction with individual lot sizes, SL/TP, and rules.
- **Risk Management:** Supports fixed lot, percent risk, and fixed money risk per trade.
- **Trailing Stop & Breakeven:** Advanced trailing stop and breakeven logic, including by profit amount.
- **Fakeout & Reversal Logic:** Detects and trades fakeouts and reversals with custom session support.
- **Daily/Weekly Loss Limits:** Automatically stops trading if loss limits are hit.
- **Visuals & Alerts:** Draws range boxes and lines, with optional stats panel and alerts.
- **End-of-Day Logic:** At 23:00 Berlin time, closes all losing trades and trades not protected by breakeven/profit SL, and deletes all pending orders.

## Usage

1. Copy `Innovationx International Rnage Breakout X.mq4` to your MT4 `Experts` directory.
2. Attach the EA to a chart and configure the input parameters as desired.
3. The EA will automatically manage range detection, trade placement, and risk controls.

## Inputs

- **Trade Sizing & Risk:** Lot size, risk percent, fixed risk, max trades, etc.
- **Profit Targets:** Take profit as R-multiple or points.
- **Stop Loss:** Range, points, or fixed price.
- **Breakeven & Trailing:** Enable/disable, trigger levels, by points, percent, or profit.
- **Session Timing:** Range/session start/end, custom sessions.
- **Fakeout/Reversal:** Enable/disable, session times, max reversal trades.
- **Loss Limits:** Daily, weekly, and emergency stop equity percent.
- **Visuals:** Range box color, stats panel, alerts.

## Notes

- The EA is designed for professional use and supports indices, forex, metals, and crypto.
- All trading logic is time-zone aware (Berlin time for EOD logic).
- For best results, backtest and optimize parameters for your instrument and broker.

## Support

For support, contact Bryan Alvin Bagorogoza  
Email/Phone: See code header  
Website: [innovationxinternational.com](https://innovationxinternational.com)